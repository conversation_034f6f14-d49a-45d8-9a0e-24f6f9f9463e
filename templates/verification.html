<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verification Form</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .verification-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
            transition: transform 0.3s ease;
        }
        .form-card:hover {
            transform: translateY(-5px);
        }
        .icon-circle {
            width: 80px;
            height: 80px;
            background-color: #e3f2fd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
        }
        .verification-input {
            letter-spacing: 0.5em;
            text-align: center;
            font-size: 1.2rem;
        }
        .success-message {
            display: none;
        }
        .loading {
            display: none;
        }
        .btn-primary {
            transition: background-color 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="form-card p-4 p-md-5">
            <div class="text-center mb-4">
                <div class="icon-circle">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="#0d6efd" class="bi bi-shield-lock" viewBox="0 0 16 16">
                        <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
                    </svg>
                </div>
                <p class="text-muted">Please enter the 6-digit code</p>
            </div>

            {% if messages %}
                <div class="alert alert-danger">
                    {% for message in messages %}
                        <p>{{ message }}</p>
                    {% endfor %}
                </div>
            {% endif %}

            <form id="verificationForm" method="post" action="{% url 'login_with_phone' %}">
                {% csrf_token %}
                
                <input type="hidden" name="session_name" value="{{ object.session_name }}">
                
                <div class="mb-4">
                    <input 
                        type="text" 
                        name="code" 
                        class="form-control verification-input" 
                        maxlength="6" 
                        placeholder="------" 
                        required>
                </div>
            
                <div class="alert alert-danger mb-4" role="alert" style="display: none;">
                    Invalid verification code. Please try again.
                </div>
            
                <button type="submit" class="btn btn-primary w-100 py-2 mb-3">
                    <span class="normal-text">Verify Code</span>
                    <span class="loading">
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        Verifying...
                    </span>
                </button>
            </form>

            <div class="success-message text-center">
                <div class="icon-circle bg-success-subtle mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="#198754" class="bi bi-check-lg" viewBox="0 0 16 16">
                        <path d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z"/>
                    </svg>
                </div>
                <h3 class="text-success mb-2">Verified Successfully!</h3>
                <p class="text-muted">Your phone number has been verified</p>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('verificationForm').addEventListener('submit', function(e) {
            const form = this;
            const input = form.querySelector('input[name="code"]');
            const errorAlert = form.querySelector('.alert-danger');
            const submitBtn = form.querySelector('button[type="submit"]');
            const normalText = submitBtn.querySelector('.normal-text');
            const loadingText = submitBtn.querySelector('.loading');

            // Show loading state
            normalText.style.display = 'none';
            loadingText.style.display = 'inline-block';
            submitBtn.disabled = true;

            // Remove the preventDefault() to allow the form to submit
        });

        document.getElementById('resendCode').addEventListener('click', function(e) {
            e.preventDefault();
            alert('Verification code resent!');
        });

        // Format input to only allow numbers
        document.querySelector('.verification-input').addEventListener('input', function(e) {
            this.value = this.value.replace(/\D/g, '').slice(0, 6);
        });
    </script>
</body>
</html>
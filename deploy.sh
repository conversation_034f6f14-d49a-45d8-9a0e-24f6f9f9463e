#!/bin/bash

echo "Enter your commit message:"
read commit_message

echo "Pushing changes to remote repository..."
git add .
git commit -m "$commit_message"
git push

sleep 1

echo "Connecting to the server and updating..."
ssh root@************** << 'EOF'
    cd /root/taxi_userbot

    git stash || echo "No changes to stash."
    git pull origin main

    if [ -f venv/bin/activate ]; then
        source venv/bin/activate
        pip3 install -r requirements.txt
    else
        echo "Python virtual environment not found!"
        exit 1
    fi
    python3 manage.py migrate
    python3 manage.py collectstatic --noinput

    systemctl restart bot.service
    systemctl restart taxi_userbot.service
EOF

echo "Deployment complete."

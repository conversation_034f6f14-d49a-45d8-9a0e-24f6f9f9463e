#!/usr/bin/env python3
"""
Test script for the send-auto-message API
"""
import requests
import json

# API endpoint
API_URL = "http://localhost:8000/bot/api/send-auto-message/"

def test_send_auto_message():
    """Test the send-auto-message API endpoint"""
    
    # Test data
    test_data = {
        "user_id": 7608799593,  # Actual user_id from database
        "text": "Bu test xabari. AUTO folderdagi barcha kanallarga yuborilmoqda."
    }
    
    print("Testing send-auto-message API...")
    print(f"URL: {API_URL}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    print("-" * 50)
    
    try:
        # Send POST request
        response = requests.post(
            API_URL,
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ API test successful!")
        else:
            print("❌ API test failed!")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error! Make sure Django server is running on localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_validation():
    """Test API validation"""
    print("\n" + "="*50)
    print("Testing API validation...")
    
    # Test missing user_id
    print("\n1. Testing missing user_id:")
    try:
        response = requests.post(API_URL, json={"text": "Test message"})
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test missing text
    print("\n2. Testing missing text:")
    try:
        response = requests.post(API_URL, json={"user_id": 123456789})
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test invalid user_id
    print("\n3. Testing invalid user_id:")
    try:
        response = requests.post(API_URL, json={"user_id": "invalid", "text": "Test"})
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_send_auto_message()
    test_validation()

#!/usr/bin/env python3
"""
Telethon-based app for sending messages to AUTO folder chats
Integrates with Django models and session management
"""
import os
import django
import asyncio
import logging
from typing import List

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from telethon import TelegramClient, types
from telethon.tl.functions.messages import GetDialogFiltersRequest
from telethon.errors import Flood<PERSON>aitError
from userbot.models import Session
from asgiref.sync import sync_to_async

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TelethonAutoFolderBot:
    """
    Telethon-based bot for working with AUTO folder chats
    """

    def __init__(self, session_name: str = None):
        self.session_name = session_name
        self.client = None
        self.session_data = None

    async def initialize_from_db(self, session_name: str = None) -> bool:
        """
        Initialize client using session data from Django database
        """
        try:
            if session_name:
                self.session_name = session_name

            if not self.session_name:
                active_session = await sync_to_async(Session.objects.filter(is_active=True).first)()
                if not active_session:
                    logger.error("No active sessions found in database")
                    return False
                self.session_name = active_session.session_name

            self.session_data = await sync_to_async(Session.objects.get)(session_name=self.session_name)

            self.client = TelegramClient(
                self.session_name,
                int(self.session_data.api_id),
                self.session_data.api_hash,
                connection_retries=3,
                retry_delay=1,
                timeout=30
            )

            logger.info(f"Initialized client for session: {self.session_name}")
            return True

        except Session.DoesNotExist:
            logger.error(f"Session '{self.session_name}' not found in database")
            return False
        except Exception as e:
            logger.error(f"Error initializing client: {e}")
            return False

    async def connect(self) -> bool:
        """
        Connect to Telegram and check authorization
        """
        try:
            await self.client.connect()

            if not await self.client.is_user_authorized():
                logger.error(f"Session {self.session_name} is not authorized")
                return False

            me = await self.client.get_me()
            logger.info(f"Connected as: {me.first_name} (@{me.username})")
            return True

        except Exception as e:
            logger.error(f"Error connecting: {e}")
            return False

    async def get_dialog_filters(self) -> List:
        """
        Get all dialog filters (folders) from Telegram
        """
        try:
            result = await self.client(GetDialogFiltersRequest())
            return result.filters
        except Exception as e:
            logger.error(f"Error getting dialog filters: {e}")
            return []

    async def get_auto_folder_chats(self) -> List[int]:
        """
        Get chat IDs from AUTO folder using Telegram's folder system
        """
        auto_chats = []

        try:
            filters = await self.get_dialog_filters()

            auto_filter = None
            for filter_obj in filters:
                if hasattr(filter_obj, 'title') and 'AUTO' in filter_obj.title.upper():
                    auto_filter = filter_obj
                    logger.info(f"Found AUTO folder: {filter_obj.title}")
                    break

            if auto_filter and hasattr(auto_filter, 'include_peers'):
                for peer in auto_filter.include_peers:
                    if isinstance(peer, types.InputPeerChat):
                        auto_chats.append(-peer.chat_id)  # Group chat ID
                    elif isinstance(peer, types.InputPeerChannel):
                        auto_chats.append(-1000000000000 - peer.channel_id)  # Channel/supergroup ID
                    elif isinstance(peer, types.InputPeerUser):
                        auto_chats.append(peer.user_id)  # User ID

                logger.info(f"Found {len(auto_chats)} chats in AUTO folder")
            else:
                logger.warning("AUTO folder not found, falling back to title-based search")
                # Fallback: search by chat titles containing "AUTO"
                auto_chats = await self.get_auto_chats_by_title()

        except Exception as e:
            logger.error(f"Error getting AUTO folder chats: {e}")
            # Fallback to title-based search
            auto_chats = await self.get_auto_chats_by_title()

        return auto_chats

    async def get_auto_chats_by_title(self) -> List[int]:
        """
        Fallback method: find chats with 'AUTO' in title
        """
        auto_chats = []

        try:
            async for dialog in self.client.iter_dialogs():
                chat_title = ""
                if hasattr(dialog.entity, 'title'):
                    chat_title = dialog.entity.title or ""
                elif hasattr(dialog.entity, 'first_name'):
                    chat_title = dialog.entity.first_name or ""

                if 'AUTO' in chat_title.upper():
                    auto_chats.append(dialog.entity.id)
                    logger.info(f"Found AUTO chat by title: {chat_title} (ID: {dialog.entity.id})")

        except Exception as e:
            logger.error(f"Error in title-based search: {e}")

        return auto_chats

    async def send_message_to_chats(self, chat_ids: List[int], message_text: str) -> dict:
        """
        Send message to multiple chats with error handling and rate limiting
        """
        results = {
            'success': [],
            'failed': [],
            'total': len(chat_ids)
        }

        if not chat_ids:
            logger.warning("No chat IDs provided")
            return results

        logger.info(f"Sending message to {len(chat_ids)} chats")

        for i, chat_id in enumerate(chat_ids, 1):
            try:
                await self.client.send_message(chat_id, message_text)
                results['success'].append(chat_id)
                logger.info(f"[{i}/{len(chat_ids)}] Message sent to chat {chat_id}")

                if i < len(chat_ids):
                    await asyncio.sleep(1)

            except FloodWaitError as e:
                logger.warning(f"Flood wait error for chat {chat_id}: waiting {e.seconds} seconds")
                await asyncio.sleep(e.seconds)
                # Retry after flood wait
                try:
                    await self.client.send_message(chat_id, message_text)
                    results['success'].append(chat_id)
                    logger.info(f"[{i}/{len(chat_ids)}] Message sent to chat {chat_id} (after flood wait)")
                except Exception as retry_error:
                    results['failed'].append({'chat_id': chat_id, 'error': str(retry_error)})
                    logger.error(f"Failed to send message to chat {chat_id} after retry: {retry_error}")

            except Exception as e:
                results['failed'].append({'chat_id': chat_id, 'error': str(e)})
                logger.error(f"Failed to send message to chat {chat_id}: {e}")

        logger.info(f"Message sending completed. Success: {len(results['success'])}, Failed: {len(results['failed'])}")
        return results

    async def display_auto_folder_chats(self) -> List[dict]:
        """
        Display all chats in AUTO folder with detailed information
        """
        logger.info("Getting AUTO folder chats information...")

        chat_ids = await self.get_auto_folder_chats()

        if not chat_ids:
            print("❌ AUTO folderida hech qanday chat topilmadi!")
            return []

        chat_info_list = []
        print(f"\n📁 AUTO FOLDER CHATLARI ({len(chat_ids)} ta chat)")
        print("=" * 60)

        for i, chat_id in enumerate(chat_ids, 1):
            try:
                # Get detailed chat information
                entity = await self.client.get_entity(chat_id)

                chat_info = {
                    'index': i,
                    'id': chat_id,
                    'title': 'Unknown',
                    'type': 'Unknown',
                    'members_count': 'Unknown',
                    'username': None
                }

                # Determine chat type and get information
                if hasattr(entity, 'title'):
                    # Group or Channel
                    chat_info['title'] = entity.title
                    chat_info['username'] = getattr(entity, 'username', None)

                    if hasattr(entity, 'broadcast'):
                        if entity.broadcast:
                            chat_info['type'] = 'Kanal'
                        else:
                            chat_info['type'] = 'Superguruh'
                            chat_info['members_count'] = getattr(entity, 'participants_count', 'Unknown')
                    else:
                        chat_info['type'] = 'Guruh'

                elif hasattr(entity, 'first_name'):
                    # Private chat
                    chat_info['title'] = f"{entity.first_name} {entity.last_name or ''}".strip()
                    chat_info['type'] = 'Shaxsiy chat'
                    chat_info['username'] = getattr(entity, 'username', None)

                # Display chat information
                print(f"{i:2d}. 📋 {chat_info['title']}")
                print(f"    🆔 ID: {chat_info['id']}")
                print(f"    📱 Turi: {chat_info['type']}")

                if chat_info['username']:
                    print(f"    👤 Username: @{chat_info['username']}")

                if chat_info['members_count'] != 'Unknown':
                    print(f"    👥 A'zolar: {chat_info['members_count']}")

                print()

                chat_info_list.append(chat_info)

            except Exception as e:
                error_info = {
                    'index': i,
                    'id': chat_id,
                    'title': 'Error',
                    'type': 'Unknown',
                    'error': str(e)
                }

                print(f"{i:2d}. ❌ Chat ID: {chat_id}")
                print(f"    🚫 Xatolik: {str(e)}")
                print()

                chat_info_list.append(error_info)

        print(f"📊 Jami: {len(chat_ids)} ta chat")
        print("=" * 60)

        return chat_info_list

    async def send_message_to_auto_chats(self, message_text: str = None) -> dict:
        """
        Main method to send message to all AUTO folder chats
        """
        if not message_text:
            message_text = "Salom! Bu AUTO jildidagi barcha chatlarga yuborilgan xabar."

        logger.info("Starting to send messages to AUTO folder chats")

        chat_ids = await self.get_auto_folder_chats()

        if not chat_ids:
            logger.warning("No chats found in AUTO folder")
            return {'success': [], 'failed': [], 'total': 0}

        results = await self.send_message_to_chats(chat_ids, message_text)
        return results

    async def disconnect(self):
        """
        Disconnect from Telegram
        """
        if self.client and self.client.is_connected():
            await self.client.disconnect()
            logger.info("Disconnected from Telegram")

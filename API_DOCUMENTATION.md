# AUTO Folder Message Sender API

Bu API user_id va text qabul qilib, shu user_id bo'yicha sessiyani ochib, AUTO folderdagi barcha kanallarga xabar yuboradi va keyin sessiyani yopadi.

## Endpoint

```
POST /bot/api/send-auto-message/
```

## Request Format

### Headers
```
Content-Type: application/json
```

### Body
```json
{
    "user_id": 123456789,
    "text": "Yuborilishi kerak bo'lgan xabar matni"
}
```

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `user_id` | integer | Yes | Telegram foydalanuvchi ID raqami |
| `text` | string | Yes | Yuborilishi kerak bo'lgan xabar matni |

## Response Format

### Success Response (200 OK)
```json
{
    "success": true,
    "message": "Message sending completed for user_id: 123456789",
    "results": {
        "success": [1001234567890, 1001234567891],
        "failed": [],
        "total_chats": 2,
        "sent_count": 2,
        "failed_count": 0
    }
}
```

### Error Responses

#### 400 Bad Request - Missing user_id
```json
{
    "success": false,
    "error": "user_id is required"
}
```

#### 400 Bad Request - Missing text
```json
{
    "success": false,
    "error": "text is required"
}
```

#### 400 Bad Request - Invalid user_id
```json
{
    "success": false,
    "error": "user_id must be a valid integer"
}
```

#### 400 Bad Request - No active session
```json
{
    "success": false,
    "error": "No active session found for user_id: 123456789",
    "results": {}
}
```

#### 400 Bad Request - Connection failed
```json
{
    "success": false,
    "error": "Failed to connect session for user_id: 123456789",
    "results": {}
}
```

#### 500 Internal Server Error
```json
{
    "success": false,
    "error": "Internal server error: error details"
}
```

## Response Fields

### Success Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `success` | boolean | API muvaffaqiyatli bajarilganligini ko'rsatadi |
| `message` | string | Muvaffaqiyat haqida xabar |
| `results` | object | Natijalar haqida batafsil ma'lumot |
| `results.success` | array | Muvaffaqiyatli yuborilgan chat ID lari |
| `results.failed` | array | Yuborilmagan chatlar va xatolik sabablari |
| `results.total_chats` | integer | AUTO folderdagi jami chatlar soni |
| `results.sent_count` | integer | Muvaffaqiyatli yuborilgan xabarlar soni |
| `results.failed_count` | integer | Yuborilmagan xabarlar soni |

## Usage Examples

### cURL Example
```bash
curl -X POST http://localhost:8000/bot/api/send-auto-message/ \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 123456789,
    "text": "Bu test xabari AUTO folderdagi barcha kanallarga yuborilmoqda."
  }'
```

### Python Example
```python
import requests
import json

url = "http://localhost:8000/bot/api/send-auto-message/"
data = {
    "user_id": 123456789,
    "text": "Bu test xabari AUTO folderdagi barcha kanallarga yuborilmoqda."
}

response = requests.post(url, json=data)
result = response.json()

if response.status_code == 200:
    print("Xabar muvaffaqiyatli yuborildi!")
    print(f"Jami chatlar: {result['results']['total_chats']}")
    print(f"Yuborilgan: {result['results']['sent_count']}")
    print(f"Xatolik: {result['results']['failed_count']}")
else:
    print(f"Xatolik: {result['error']}")
```

### JavaScript Example
```javascript
const url = 'http://localhost:8000/bot/api/send-auto-message/';
const data = {
    user_id: 123456789,
    text: 'Bu test xabari AUTO folderdagi barcha kanallarga yuborilmoqda.'
};

fetch(url, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
})
.then(response => response.json())
.then(result => {
    if (result.success) {
        console.log('Xabar muvaffaqiyatli yuborildi!');
        console.log(`Jami chatlar: ${result.results.total_chats}`);
        console.log(`Yuborilgan: ${result.results.sent_count}`);
        console.log(`Xatolik: ${result.results.failed_count}`);
    } else {
        console.log(`Xatolik: ${result.error}`);
    }
})
.catch(error => console.error('Error:', error));
```

## Important Notes

1. **Session Requirement**: API ishlashi uchun berilgan `user_id` ga tegishli aktiv sessiya mavjud bo'lishi kerak.

2. **AUTO Folder**: Xabarlar faqat "AUTO" nomli folderdagi chatlarga yuboriladi.

3. **Rate Limiting**: Flood limitlarini oldini olish uchun har bir xabar o'rtasida 1 soniya kutiladi.

4. **Session Management**: API avtomatik ravishda sessiyani ochadi va yopadi.

5. **Error Handling**: Agar ba'zi chatlarga xabar yuborilmasa, API boshqa chatlarga yuborishni davom ettiradi.

## Testing

API ni test qilish uchun `test_api.py` faylini ishlatishingiz mumkin:

```bash
python test_api.py
```

Bu script API ning to'g'ri ishlashini va validatsiyasini tekshiradi.

from django.urls import reverse
from django.contrib import messages
from django.core.cache import cache
from django.shortcuts import redirect

from userbot.models import Session
from userbot.service import userbot


# pylint: disable=E1101
def login_with_phone(request):
    """
    View to login with a phone number.
    """
    code = request.POST.get("code")
    session_name = request.POST.get("session_name")
    phone_code_hash = cache.get(f"phone_code_hash_{session_name}")

    try:
        session_obj = Session.get_by_session_name(session_name)
    except Session.DoesNotExist:
        messages.error(
            request,
            f"Session {session_name} does not exist",
        )
        url = reverse("admin:userbot_session_changelist")
        return redirect(url)

    try:
        userbot.verify_code(
            session_name=session_name,
            code=code,
            phone_code_hash=phone_code_hash,
            two_fa_password=session_obj.two_fa_password,
        )
        url = reverse("admin:userbot_session_changelist")
        messages.success(
            request,
            f"Session {session_name} verified successfully",
        )
        session_obj.mark_as_active()
        return redirect(url)

    # pylint: disable=broad-exception-caught
    except Exception as exc:
        messages.error(
            request,
            f"Invalid verification code. for {session_name} error: {exc}",
        )
        url = reverse("admin:userbot_session_changelist")
        return redirect(url)

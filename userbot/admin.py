from django.contrib import admin
from django.db.models import Model
from django.core.cache import cache
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse

from unfold.admin import ModelAdmin, TabularInline

from userbot.models import Session, BotUser
from userbot.service import userbot


class SessionAdmin(ModelAdmin):
    """
    Admin class for Session model.
    """
    list_display = ("session_name", "number", "user_id", "is_active")
    list_filter = ("is_active",)
    search_fields = ("session_name", "number", "user_id")
    ordering = ("-id",)

    def get_fields(self, request, obj=None):
        fields = super().get_fields(request, obj)
        if not obj:
            fields = [field for field in fields if field != "is_active"]
        return fields

    def response_add(
        self,
        request: HttpRequest,
        obj: Model,
        post_url_continue: str | None = None,
    ) -> HttpResponse:
        phone_code_hash = userbot.send_verification_code(obj.session_name)
        cache.set(
            key=f"phone_code_hash_{obj.session_name}",
            value=phone_code_hash,
            timeout=60 * 60,
        )
        return TemplateResponse(
            request=request,
            template="verification.html",
            context={
                "object": obj,
            },
        )


class BotUserAdmin(ModelAdmin):
    """
    Admin class for BotUser model.
    """
    list_display = ("user_id", "first_name", "username", "language_code", "is_premium", "created_at")
    list_filter = ("is_bot", "is_premium", "language_code", "created_at")
    search_fields = ("user_id", "username", "first_name", "last_name")
    ordering = ("-created_at",)
    readonly_fields = ("user_id", "created_at", "updated_at")

    fieldsets = (
        ("Asosiy ma'lumotlar", {
            'fields': ('user_id', 'username', 'first_name', 'last_name')
        }),
        ("Qo'shimcha ma'lumotlar", {
            'fields': ('language_code', 'is_bot', 'is_premium')
        }),
        ("Vaqt ma'lumotlari", {
            'fields': ('created_at', 'updated_at')
        }),
    )


admin.site.register(Session, SessionAdmin)
admin.site.register(BotUser, BotUserAdmin)

import os
import json
import asyncio
import logging

from telethon import Telegram<PERSON><PERSON>, events

from backend.settings import STATIC_ROOT
from userbot.external.telethon.controller import ImprovedFloodController


logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

ADMIN = 4662798326
MAX_RETRIES = 5
RETRY_DELAY = 1
CONNECTION_TIMEOUT = 30


async def run_client(session_name, api_id, api_hash):
    """Start and run a Telegram client with flood control."""
    flood_controller = ImprovedFloodController(
        rate_limit=20,
        cleanup_interval=3600,
        flood_multiplier=1.5,
        global_limit=30,
    )

    for attempt in range(MAX_RETRIES):
        try:
            client = TelegramClient(
                session_name,
                api_id,
                api_hash,
                connection_retries=3,
                retry_delay=RETRY_DELAY,
                timeout=CONNECTION_TIMEOUT
            )

            await client.connect()

            if not await client.is_user_authorized():
                logger.error(f"Session {session_name} is not authorized")
                return

            # Get user information for logging
            me = await client.get_me()
            logger.info(f"{session_name} has started successfully! User: {me.first_name} (@{me.username}) ID: {me.id}")

            @client.on(events.NewMessage(incoming=True))
            async def message_handler(event: events.NewMessage.Event):
                if not event.message or not event.message.text or not event.is_group:
                    return

                if not event.chat or not event.message:
                    return

                try:
                    logger.info(f"Message received in {session_name}: {event.message.text[:50]}...")

                except Exception as e:
                    logger.error(
                        f"Error processing message in {session_name}: {str(e)}"
                    )

            await client.run_until_disconnected()
            break

        except Exception as e:
            logger.error(f"Failed to start {session_name}: {str(e)}")
            raise

        finally:
            print("Client disconnected successfully")
            await client.disconnect()


def read_client_configs(file_path=os.path.join(STATIC_ROOT, "sessions.json")):
    """Read and filter active client configurations."""
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            data = json.load(file)
            return [item for item in data if item.get("is_active", False)]
    except Exception as e:
        logger.error(f"Error reading client configs: {str(e)}")
        return []


async def run_multiple_clients(client_configs):
    """Run multiple Telegram clients concurrently."""
    if not client_configs:
        logger.error("No active client configurations found")
        return

    tasks = []
    for config in client_configs:
        task = run_client(
            session_name=config["session_name"],
            api_id=config["api_id"],
            api_hash=config["api_hash"],
        )
        tasks.append(task)
        await asyncio.sleep(2)

    try:
        await asyncio.gather(*tasks)
    except Exception as e:
        logger.error(f"Error running clients: {str(e)}")


def run_bot():
    """Initialize and run the Telegram bot."""
    try:
        configs = read_client_configs()
        if configs:
            logger.info(f"Starting bot with {len(configs)} active clients")
            asyncio.run(run_multiple_clients(configs))
        else:
            logger.error("No client configurations available")
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot crashed: {str(e)}")


if __name__ == "__main__":
    run_bot()

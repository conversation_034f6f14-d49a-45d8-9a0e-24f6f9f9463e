import random
import logging

from collections import defaultdict
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from telethon.errors import FloodWaitError


logger = logging.getLogger(__name__)


class ImprovedFloodController:
    """
    Enhanced flood controller with better
    memory management and sophisticated rate limiting.
    """
    def __init__(
        self,
        rate_limit: int = 20,
        cleanup_interval: int = 3600,
        flood_multiplier: float = 1.5,
        global_limit: int = 30
    ):
        self.rate_limit = rate_limit  # per minute per chat
        self.global_limit = global_limit  # total messages per minute
        self.flood_multiplier = flood_multiplier
        self.cleanup_interval = cleanup_interval  # seconds

        # Store timestamps with TTL
        self.message_timestamps: Dict[int, List[datetime]] = defaultdict(list)
        self.global_timestamps: List[datetime] = []
        self.retry_delays: Dict[int, float] = defaultdict(lambda: 1)
        self.last_cleanup = datetime.now()

        # Track consecutive errors
        self.error_counts: Dict[int, int] = defaultdict(int)
        self.max_consecutive_errors = 5

        # Chat-specific cooldowns
        self.chat_cooldowns: Dict[int, datetime] = {}

    async def cleanup_old_data(self):
        """Remove old timestamps and reset error counts periodically."""
        now = datetime.now()
        if (now - self.last_cleanup).total_seconds() < self.cleanup_interval:
            return

        minute_ago = now - timedelta(minutes=1)

        # Cleanup message timestamps
        for chat_id in list(self.message_timestamps.keys()):
            self.message_timestamps[chat_id] = [
                ts for ts in self.message_timestamps[chat_id]
                if ts > minute_ago
            ]
            if not self.message_timestamps[chat_id]:
                del self.message_timestamps[chat_id]

        # Cleanup global timestamps
        self.global_timestamps = [
            ts for ts in self.global_timestamps if ts > minute_ago]

        # Reset error counts for chats without recent errors
        for chat_id in list(self.error_counts.keys()):
            if chat_id not in self.message_timestamps:
                del self.error_counts[chat_id]

        # Cleanup expired cooldowns
        for chat_id in list(self.chat_cooldowns.keys()):
            if self.chat_cooldowns[chat_id] <= now:
                del self.chat_cooldowns[chat_id]

        self.last_cleanup = now

    async def can_send_message(
        self, chat_id: int
    ) -> tuple[bool, Optional[float]]:
        """
        Check if a message can be sent to the specified chat.
        Returns (can_send, wait_time).
        """
        await self.cleanup_old_data()
        now = datetime.now()

        if chat_id in self.chat_cooldowns:
            if now < self.chat_cooldowns[chat_id]:
                wait_time = (
                    self.chat_cooldowns[chat_id] - now).total_seconds()
                return False, wait_time

        minute_ago = now - timedelta(minutes=1)
        global_count = len(
            [ts for ts in self.global_timestamps if ts > minute_ago])
        if global_count >= self.global_limit:
            return False, 60/self.global_limit

        chat_count = len(
            [ts for ts in self.message_timestamps[chat_id] if ts > minute_ago])
        if chat_count >= self.rate_limit:
            return False, 60/self.rate_limit

        return True, None

    def record_message(self, chat_id: int):
        """Record a successful message send."""
        now = datetime.now()
        self.message_timestamps[chat_id].append(now)
        self.global_timestamps.append(now)
        self.error_counts[chat_id] = 0  # Reset error count on success

    def handle_error(self, chat_id: int, error: Exception) -> float:
        """
        Handle an error and return the delay before next retry.
        Implements exponential backoff with jitter.
        """
        self.error_counts[chat_id] += 1
        base_delay = self.retry_delays[chat_id]

        if isinstance(error, FloodWaitError):
            delay = error.seconds * self.flood_multiplier
        else:
            delay = base_delay * self.flood_multiplier

        jitter = delay * 0.1
        delay = delay + (random.random() * jitter)

        self.retry_delays[chat_id] = min(delay, 300)

        if self.error_counts[chat_id] >= self.max_consecutive_errors:
            self.chat_cooldowns[
                chat_id] = datetime.now() + timedelta(minutes=5)
            logger.warning(
                f"Chat {chat_id} cooldown activated due to consecutive errors"
            )

        return delay

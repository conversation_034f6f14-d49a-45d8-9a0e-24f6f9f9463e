# myapp/management/commands/run_bot.py
from django.core.management.base import BaseCommand
import asyncio
import threading


class Command(BaseCommand):
    help = 'Runs the Telethon bot alongside Django'

    def handle(self, *args, **options):
        def run_bot_thread():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            from userbot.external.telethon.handlers import run_bot
            loop.run_until_complete(run_bot())
            loop.close()

        bot_thread = threading.Thread(target=run_bot_thread, daemon=True)
        bot_thread.start()

        self.stdout.write(self.style.SUCCESS('Bo<PERSON> started successfully'))

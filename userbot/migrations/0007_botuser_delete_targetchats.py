# Generated by Django 5.1.5 on 2025-07-25 18:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('userbot', '0006_telegramsession_delete_telethonsession'),
    ]

    operations = [
        migrations.CreateModel(
            name='BotUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.BigIntegerField(unique=True)),
                ('username', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('first_name', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('last_name', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('language_code', models.Char<PERSON>ield(blank=True, max_length=10, null=True)),
                ('is_bot', models.BooleanField(default=False)),
                ('is_premium', models.<PERSON>oleanField(default=False)),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Bot User',
                'verbose_name_plural': 'Bot Users',
            },
        ),
    ]

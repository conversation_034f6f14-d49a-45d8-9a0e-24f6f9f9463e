# Generated by Django 5.1.5 on 2025-07-25 18:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('userbot', '0006_telegramsession_delete_telethonsession'),
    ]

    operations = [
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.BigIntegerField(unique=True, verbose_name='Telegram User ID')),
                ('username', models.CharField(blank=True, max_length=255, null=True, verbose_name='Username')),
                ('first_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='Ism')),
                ('last_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='Familiya')),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='Telefon raqam')),
                ('is_active', models.BooleanField(default=False, verbose_name='Faol')),
                ('start_date', models.DateTimeField(blank=True, null=True, verbose_name='Boshlanish sanasi')),
                ('end_date', models.DateTimeField(blank=True, null=True, verbose_name='Tugash sanasi')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Yaratilgan sana')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Yangilangan sana')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Izohlar')),
            ],
            options={
                'verbose_name': 'Subscription',
                'verbose_name_plural': 'Subscriptions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.DeleteModel(
            name='TargetChats',
        ),
    ]

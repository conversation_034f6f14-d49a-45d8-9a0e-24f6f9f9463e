# Generated by Django 5.1.5 on 2025-01-22 21:25

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Session",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("api_id", models.<PERSON>r<PERSON><PERSON>(max_length=255, unique=True)),
                ("api_hash", models.Char<PERSON>ield(max_length=255, unique=True)),
                ("session_name", models.Char<PERSON>ield(max_length=255, unique=True)),
                ("number", models.<PERSON>r<PERSON>ield(max_length=15, unique=True)),
                ("is_active", models.<PERSON><PERSON>an<PERSON>ield(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="Verification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("phone_number", models.<PERSON><PERSON><PERSON><PERSON>(max_length=15)),
                ("code", models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ("is_verified", models.<PERSON><PERSON><PERSON><PERSON><PERSON>(default=False)),
            ],
        ),
    ]

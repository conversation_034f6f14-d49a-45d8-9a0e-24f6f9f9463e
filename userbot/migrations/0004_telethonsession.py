# Generated by Django 5.1.5 on 2025-01-24 11:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("userbot", "0003_targetchats"),
    ]

    operations = [
        migrations.CreateModel(
            name="TelethonSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_id", models.CharField(max_length=255, unique=True)),
                ("dc_id", models.IntegerField(null=True)),
                ("server_address", models.CharField(max_length=255, null=True)),
                ("port", models.IntegerField(null=True)),
                ("auth_key", models.BinaryField(null=True)),
                ("user_id", models.BigIntegerField(null=True)),
                ("date", models.DateTimeField(auto_now=True)),
            ],
            options={
                "db_table": "telethon_sessions",
            },
        ),
    ]

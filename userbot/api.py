#!/usr/bin/env python3
"""
API endpoints for userbot functionality
"""
import os
import django
import asyncio
import logging
from typing import List

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.http import JsonResponse

from telethon import TelegramClient, types
from telethon.tl.functions.messages import GetDialogFiltersRequest
from telethon.errors import FloodWaitError
from userbot.models import Session
from asgiref.sync import sync_to_async

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TelethonMessageSender:
    """
    Telethon-based message sender for AUTO folder chats
    """

    def __init__(self, user_id: int):
        self.user_id = user_id
        self.client = None
        self.session_data = None

    async def initialize_from_user_id(self) -> bool:
        """
        Initialize client using user_id from Django database
        """
        try:
            self.session_data = await sync_to_async(
                Session.objects.filter(user_id=self.user_id, is_active=True).first
            )()
            
            if not self.session_data:
                logger.error(f"No active session found for user_id: {self.user_id}")
                return False

            self.client = TelegramClient(
                self.session_data.session_name,
                int(self.session_data.api_id),
                self.session_data.api_hash,
                connection_retries=3,
                retry_delay=1,
                timeout=30
            )

            logger.info(f"Initialized client for user_id: {self.user_id}")
            return True

        except Exception as e:
            logger.error(f"Error initializing client for user_id {self.user_id}: {e}")
            return False

    async def connect(self) -> bool:
        """
        Connect to Telegram and check authorization
        """
        try:
            await self.client.connect()

            if not await self.client.is_user_authorized():
                logger.error(f"Session for user_id {self.user_id} is not authorized")
                return False

            me = await self.client.get_me()
            logger.info(f"Connected as: {me.first_name} (@{me.username}) ID: {me.id}")
            return True

        except Exception as e:
            logger.error(f"Error connecting for user_id {self.user_id}: {e}")
            return False

    async def get_dialog_filters(self) -> List:
        """
        Get all dialog filters (folders) from Telegram
        """
        try:
            result = await self.client(GetDialogFiltersRequest())
            return result.filters
        except Exception as e:
            logger.error(f"Error getting dialog filters: {e}")
            return []

    async def get_auto_folder_chats(self) -> List[int]:
        """
        Get chat IDs from AUTO folder using Telegram's folder system
        """
        auto_chats = []

        try:
            filters = await self.get_dialog_filters()

            auto_filter = None
            for filter_obj in filters:
                if hasattr(filter_obj, 'title') and 'AUTO' in filter_obj.title.upper():
                    auto_filter = filter_obj
                    logger.info(f"Found AUTO folder: {filter_obj.title}")
                    break

            if auto_filter and hasattr(auto_filter, 'include_peers'):
                for peer in auto_filter.include_peers:
                    if isinstance(peer, types.InputPeerChat):
                        auto_chats.append(-peer.chat_id)  # Group chat ID
                    elif isinstance(peer, types.InputPeerChannel):
                        auto_chats.append(-1000000000000 - peer.channel_id)  # Channel/supergroup ID
                    elif isinstance(peer, types.InputPeerUser):
                        auto_chats.append(peer.user_id)  # User ID

            logger.info(f"Found {len(auto_chats)} chats in AUTO folder")
            return auto_chats

        except Exception as e:
            logger.error(f"Error getting AUTO folder chats: {e}")
            return []

    async def send_message_to_auto_chats(self, message_text: str) -> dict:
        """
        Send message to all chats in AUTO folder
        """
        results = {
            'success': [],
            'failed': [],
            'total_chats': 0,
            'sent_count': 0,
            'failed_count': 0
        }

        try:
            auto_chats = await self.get_auto_folder_chats()
            results['total_chats'] = len(auto_chats)

            if not auto_chats:
                logger.warning("No chats found in AUTO folder")
                return results

            for chat_id in auto_chats:
                try:
                    # Try to get the entity first to ensure it's accessible
                    try:
                        entity = await self.client.get_entity(chat_id)
                        await self.client.send_message(entity, message_text)
                        results['success'].append(chat_id)
                        results['sent_count'] += 1
                        logger.info(f"Message sent to chat {chat_id}")
                    except Exception:
                        # If entity not found, try with chat_id directly
                        await self.client.send_message(chat_id, message_text)
                        results['success'].append(chat_id)
                        results['sent_count'] += 1
                        logger.info(f"Message sent to chat {chat_id} (direct)")

                    # Small delay to avoid flood limits
                    await asyncio.sleep(1)

                except FloodWaitError as e:
                    logger.warning(f"Flood wait for {e.seconds} seconds")
                    await asyncio.sleep(e.seconds)
                    try:
                        entity = await self.client.get_entity(chat_id)
                        await self.client.send_message(entity, message_text)
                        results['success'].append(chat_id)
                        results['sent_count'] += 1
                    except Exception as retry_e:
                        results['failed'].append({'chat_id': chat_id, 'error': str(retry_e)})
                        results['failed_count'] += 1

                except Exception as e:
                    results['failed'].append({'chat_id': chat_id, 'error': str(e)})
                    results['failed_count'] += 1
                    logger.error(f"Error sending message to chat {chat_id}: {e}")

        except Exception as e:
            logger.error(f"Error in send_message_to_auto_chats: {e}")
            results['failed'].append({'error': str(e)})

        return results

    async def disconnect(self):
        """
        Disconnect from Telegram
        """
        try:
            if self.client:
                await self.client.disconnect()
                logger.info(f"Disconnected client for user_id: {self.user_id}")
        except Exception as e:
            logger.error(f"Error disconnecting client: {e}")


async def send_message_to_auto_folder(user_id: int, message_text: str) -> dict:
    """
    Main function to send message to AUTO folder chats
    """
    sender = TelethonMessageSender(user_id)
    
    try:
        # Initialize and connect
        if not await sender.initialize_from_user_id():
            return {
                'success': False,
                'error': f'No active session found for user_id: {user_id}',
                'results': {}
            }

        if not await sender.connect():
            return {
                'success': False,
                'error': f'Failed to connect session for user_id: {user_id}',
                'results': {}
            }

        # Send messages
        results = await sender.send_message_to_auto_chats(message_text)
        
        return {
            'success': True,
            'message': f'Message sending completed for user_id: {user_id}',
            'results': results
        }

    except Exception as e:
        logger.error(f"Error in send_message_to_auto_folder: {e}")
        return {
            'success': False,
            'error': str(e),
            'results': {}
        }
    
    finally:
        # Always disconnect
        await sender.disconnect()


@api_view(['POST'])
def send_auto_message(request):
    """
    API endpoint to send message to AUTO folder chats
    
    POST /api/send-auto-message/
    {
        "user_id": 123456789,
        "text": "Your message text here"
    }
    """
    try:
        user_id = request.data.get('user_id')
        text = request.data.get('text')

        # Validate input
        if not user_id:
            return Response({
                'success': False,
                'error': 'user_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not text:
            return Response({
                'success': False,
                'error': 'text is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user_id = int(user_id)
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'user_id must be a valid integer'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Send message asynchronously
        result = asyncio.run(send_message_to_auto_folder(user_id, text))
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error in send_auto_message API: {e}")
        return Response({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

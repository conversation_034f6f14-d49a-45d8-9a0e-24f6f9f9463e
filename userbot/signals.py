import os
import json

from django.dispatch import receiver
from django.db.models.signals import post_save, post_delete

from userbot.service import userbot
from userbot.models import Session

from backend.settings import STATIC_ROOT


SESSIONS_FILE = os.path.join(STATIC_ROOT, "sessions.json")


# pylint: disable=unused-argument
@receiver(post_save, sender=Session)
def add_session(sender, instance, **kwargs):
    """
    Signal to add a session to the database.
    """
    with open(SESSIONS_FILE, "r", encoding="utf-8") as f:
        data = json.load(f)
        data.append(
            {
                "api_id": instance.api_id,
                "api_hash": instance.api_hash,
                "session_name": instance.session_name,
                "number": instance.number,
                "user_id": instance.user_id,
                "is_active": instance.is_active,
            }
        )
        with open(SESSIONS_FILE, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=4)


# pylint: disable=unused-argument
@receiver(post_delete, sender=Session)
def delete_session(sender, instance: Session, **kwargs):
    """
    Signal to delete a session from the database.
    """
    userbot.logout(instance.session_name)

    with open(SESSIONS_FILE, "r", encoding="utf-8") as f:
        data = json.load(f)

        data = [
            item for item in data
            if item["api_id"] != instance.api_id
        ]
        with open(SESSIONS_FILE, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=4)
